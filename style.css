@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

body {
    position: relative; /* Set body as reference for absolute positioning */
    min-height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
    font-family: '<PERSON><PERSON>', serif; /* Use Amiri font, fallback to generic serif */
    direction: rtl; /* Right-to-left for Arabic text */
}

.container {
    position: absolute; /* Absolute positioning */
    top: 20px; /* Distance from top */
    right: 20px; /* Distance from right */
    border: 5px solid black; /* Black border */
    padding: 30px;
    text-align: center;
    background-color: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    display: flex; /* Use flexbox to align h1 and QR code */
    flex-direction: row-reverse; /* Put QR code on the left of h1 */
    align-items: center; /* Vertically align items */
    gap: 20px; /* Space between h1 and QR code */
}

h1 {
    font-size: 4em; /* Large font size */
    color: #333; /* Dark grey color */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2); /* Text shadow for depth */
    margin: 0;
}

.qr-code {
    border: 5px solid purple; /* Purple border for QR code */
    width: 150px; /* Set a fixed width for the QR code */
    height: 150px; /* Set a fixed height for the QR code */
} 